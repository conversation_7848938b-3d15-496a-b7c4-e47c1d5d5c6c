// Simple performance test script to check company page loading
// Using built-in fetch (Node.js 18+)

async function testCompanyPagePerformance() {
  const companyId = 'b8163a34-4104-4fb7-ae37-d819aa56b389'; // Use the same company ID from the logs
  const baseUrl = 'http://localhost:3001';
  
  console.log('Testing company page performance...');
  console.log(`Company ID: ${companyId}`);
  console.log('---');
  
  try {
    // Test the main company API endpoint
    console.log('1. Testing company API endpoint...');
    const startTime = Date.now();
    const response = await fetch(`${baseUrl}/api/companies/${companyId}`);
    const endTime = Date.now();
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✓ Company API: ${endTime - startTime}ms`);
      console.log(`  - Company: ${data.name}`);
      console.log(`  - Benefits: ${data.company_benefits?.length || 0}`);
      
      // Test batch benefit verifications if there are benefits
      if (data.company_benefits && data.company_benefits.length > 0) {
        console.log('\n2. Testing batch benefit verifications...');
        const benefitIds = data.company_benefits.map(cb => cb.id);
        
        const batchStartTime = Date.now();
        const batchResponse = await fetch(`${baseUrl}/api/benefit-verifications/batch`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            companyBenefitIds: benefitIds
          })
        });
        const batchEndTime = Date.now();
        
        if (batchResponse.ok) {
          const batchData = await batchResponse.json();
          console.log(`✓ Batch verifications: ${batchEndTime - batchStartTime}ms`);
          console.log(`  - Processed ${Object.keys(batchData).length} benefits`);
        } else {
          console.log(`✗ Batch verifications failed: ${batchResponse.status}`);
        }
        
        // Compare with individual requests
        console.log('\n3. Testing individual benefit verifications (for comparison)...');
        const individualStartTime = Date.now();
        const individualPromises = benefitIds.slice(0, 3).map(id => // Test only first 3 to avoid spam
          fetch(`${baseUrl}/api/benefit-verifications/${id}`)
        );
        await Promise.all(individualPromises);
        const individualEndTime = Date.now();
        console.log(`✓ Individual verifications (3 benefits): ${individualEndTime - individualStartTime}ms`);
      }
      
      // Test analytics tracking
      console.log('\n4. Testing batch analytics tracking...');
      const analyticsStartTime = Date.now();
      const analyticsResponse = await fetch(`${baseUrl}/api/analytics/track`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'batch_benefit_interaction',
          data: {
            benefitIds: data.company_benefits?.slice(0, 5).map(cb => cb.benefit?.id).filter(Boolean) || [],
            companyId: companyId,
            interactionType: 'view'
          }
        })
      });
      const analyticsEndTime = Date.now();
      
      if (analyticsResponse.ok) {
        console.log(`✓ Batch analytics: ${analyticsEndTime - analyticsStartTime}ms`);
      } else {
        console.log(`✗ Batch analytics failed: ${analyticsResponse.status}`);
      }
      
    } else {
      console.log(`✗ Company API failed: ${response.status}`);
    }
    
  } catch (error) {
    console.error('Error testing performance:', error.message);
  }
}

// Run the test
testCompanyPagePerformance();
