import { useState, useEffect, useCallback } from 'react'

interface VerificationCounts {
  confirmed: number
  disputed: number
  total: number
}

interface BatchVerificationHook {
  getVerificationCounts: (companyBenefitId: string) => VerificationCounts | null
  isLoading: boolean
  error: string | null
}

// Global cache to store verification counts and avoid duplicate requests
const verificationCache = new Map<string, VerificationCounts>()
const pendingRequests = new Map<string, Promise<void>>()

export function useBatchBenefitVerifications(companyBenefitIds: string[]): BatchVerificationHook {
  const [verificationCounts, setVerificationCounts] = useState<Record<string, VerificationCounts>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchBatchVerifications = useCallback(async (ids: string[]) => {
    // Filter out IDs that are already cached or being requested
    const uncachedIds = ids.filter(id => 
      !verificationCache.has(id) && !pendingRequests.has(id)
    )

    if (uncachedIds.length === 0) {
      // All data is already cached or being fetched
      const cachedData: Record<string, VerificationCounts> = {}
      ids.forEach(id => {
        const cached = verificationCache.get(id)
        if (cached) {
          cachedData[id] = cached
        }
      })
      setVerificationCounts(cachedData)
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Create a promise for this batch request
      const batchPromise = fetch('/api/benefit-verifications/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyBenefitIds: uncachedIds
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        return response.json()
      })
      .then((data: Record<string, VerificationCounts>) => {
        // Cache the results
        Object.entries(data).forEach(([id, counts]) => {
          verificationCache.set(id, counts)
        })

        // Update state with all requested data (cached + new)
        const allData: Record<string, VerificationCounts> = {}
        ids.forEach(id => {
          const cached = verificationCache.get(id)
          if (cached) {
            allData[id] = cached
          }
        })
        setVerificationCounts(allData)
      })
      .catch(err => {
        console.error('Error fetching batch verification counts:', err)
        setError(err.message)
      })
      .finally(() => {
        // Remove from pending requests
        uncachedIds.forEach(id => {
          pendingRequests.delete(id)
        })
        setIsLoading(false)
      })

      // Mark these IDs as being requested
      uncachedIds.forEach(id => {
        pendingRequests.set(id, batchPromise)
      })

      await batchPromise

    } catch (err) {
      console.error('Error in fetchBatchVerifications:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    if (companyBenefitIds.length > 0) {
      fetchBatchVerifications(companyBenefitIds)
    } else {
      setIsLoading(false)
    }
  }, [companyBenefitIds, fetchBatchVerifications])

  const getVerificationCounts = useCallback((companyBenefitId: string): VerificationCounts | null => {
    return verificationCounts[companyBenefitId] || verificationCache.get(companyBenefitId) || null
  }, [verificationCounts])

  return {
    getVerificationCounts,
    isLoading,
    error
  }
}
